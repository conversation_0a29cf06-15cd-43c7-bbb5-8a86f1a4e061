/* Categories Page Styles */
.categories-page {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--bg-secondary);
  min-height: 100vh;
}

/* Header Section */
.categories-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.page-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.btn-icon {
  width: 1rem;
  height: 1rem;
}

/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  font-size: 2rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: var(--bg-tertiary);
}

.stat-card.total .stat-icon {
  background: #dbeafe;
}

.stat-card.active .stat-icon {
  background: #dcfce7;
}

.stat-card.featured .stat-icon {
  background: #fef3c7;
}

.stat-card.inactive .stat-icon {
  background: #fee2e2;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-card.total .stat-number {
  color: #2563eb;
}

.stat-card.active .stat-number {
  color: #059669;
}

.stat-card.featured .stat-number {
  color: #d97706;
}

.stat-card.inactive .stat-number {
  color: #dc2626;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Controls Section */
.controls-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.search-container {
  margin-bottom: 1.5rem;
}

.search-input-wrapper {
  position: relative;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  font-size: 1rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background: var(--bg-primary);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.filters-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.filter-icon {
  color: var(--text-secondary);
  font-size: 1rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-medium);
}

.filter-btn.active {
  background: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.filter-btn-icon {
  font-size: 1rem;
}

.view-toggle {
  display: flex;
  gap: 0.25rem;
  background: var(--bg-tertiary);
  padding: 0.25rem;
  border-radius: var(--radius-md);
}

.view-btn {
  padding: 0.5rem;
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 1rem;
}

.view-btn:hover {
  background: var(--bg-primary);
}

.view-btn.active {
  background: var(--primary-color);
  color: var(--text-white);
}

/* Categories Section */
.categories-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.search-results {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-style: italic;
}

.section-content {
  padding: 1.5rem;
}

/* Loading and Empty States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: var(--text-secondary);
  margin: 0 0 1.5rem 0;
}

/* Status Badge */
.category-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.category-status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.category-status-badge.inactive {
  background: #fee2e2;
  color: #991b1b;
}

/* Categories Grid View */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.category-card {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.category-card.featured {
  border-color: #facc15;
  box-shadow: 0 4px 6px -1px rgb(250 204 21 / 0.1);
}

.category-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.category-info {
  display: flex;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.category-icon-wrapper {
  flex-shrink: 0;
}

.category-image {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.category-icon-placeholder {
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-icon {
  font-size: 1.5rem;
}

.category-details {
  flex: 1;
  min-width: 0;
}

.category-title-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.category-name {
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.featured-badge {
  background: #facc15;
  color: #78350f;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-weight: 500;
  flex-shrink: 0;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.action-btn {
  padding: 0.5rem;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.action-btn.delete {
  background: rgba(239, 68, 68, 0.2);
}

.action-btn.delete:hover {
  background: rgba(239, 68, 68, 0.3);
}

.category-card-content {
  margin-bottom: 1rem;
}

.category-short-description {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.9;
  margin: 0 0 0.75rem 0;
}

.category-description {
  font-size: 0.875rem;
  opacity: 0.75;
  margin: 0 0 0.75rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.category-tag {
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.category-tag.more {
  font-weight: 500;
}

.category-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.category-meta {
  display: flex;
  gap: 0.75rem;
  font-size: 0.75rem;
  opacity: 0.75;
}

.meta-item {
  white-space: nowrap;
}

.category-visibility {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
}

.visibility-icon {
  width: 0.75rem;
  height: 0.75rem;
}

.visibility-text {
  font-size: 0.75rem;
}

/* Categories Table View */
.categories-table {
  overflow-x: auto;
}

.categories-table .table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
}

.categories-table .table th {
  background: var(--bg-secondary);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  white-space: nowrap;
}

.categories-table .table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
}

.categories-table .table tbody tr:hover {
  background: var(--bg-secondary);
}

.table-category-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.table-category-icon {
  flex-shrink: 0;
}

.table-category-image {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-md);
  object-fit: cover;
}

.table-icon {
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.table-category-details {
  min-width: 0;
  flex: 1;
}

.table-category-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-featured-badge {
  font-size: 0.875rem;
}

.table-category-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
}

.table-order {
  font-weight: 500;
  color: var(--text-primary);
}

.table-services {
  font-weight: 500;
  color: var(--text-primary);
}

.table-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  max-width: 200px;
}

.table-tag {
  font-size: 0.75rem;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

.table-tag-more {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

.table-no-tags {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-style: italic;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
}

.table-action-btn {
  padding: 0.5rem;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-action-btn:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
  transform: scale(1.05);
}

.table-action-btn.edit:hover {
  background: #dbeafe;
  color: #2563eb;
}

.table-action-btn.delete:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .categories-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .categories-page {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .filter-buttons {
    justify-content: center;
  }

  .view-toggle {
    align-self: center;
  }

  .categories-table {
    font-size: 0.875rem;
  }

  .table-category-description {
    max-width: 150px;
  }

  .table-tags {
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
  }

  .btn {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-btn-text {
    display: none;
  }

  .categories-table .table th,
  .categories-table .table td {
    padding: 0.5rem;
  }
}
